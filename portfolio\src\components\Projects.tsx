'use client';

import { motion } from 'framer-motion';
import { ExternalLink, Github, Heart, Brain, Film, Zap, Cloud, QrCode } from 'lucide-react';

export default function Projects() {
  const projects = [
    {
      title: "Diabetes Prediction ML Pipeline",
      description: "Comprehensive machine learning pipeline for diabetes prediction using ensemble methods with 82.04% ROC AUC. Features advanced preprocessing, feature engineering, and interactive Streamlit web interface.",
      icon: <Heart className="w-8 h-8" />,
      technologies: ["Python", "scikit-learn", "XGBoost", "Streamlit", "SMOTE"],
      github: "https://github.com/LazyCr0w/Diabetes_Prediction",
      highlights: ["82.04% ROC AUC", "5 ML Models Ensemble", "25+ Visualizations", "Interactive Web App"],
      category: "Healthcare ML"
    },
    {
      title: "Viral Meme Predictor",
      description: "Machine learning application that predicts meme virality by analyzing both text and image features. Combines NLP and computer vision to extract meaningful patterns from Reddit data.",
      icon: <Brain className="w-8 h-8" />,
      technologies: ["Python", "OpenCV", "TextBlob", "PRAW", "scikit-learn"],
      github: "https://github.com/LazyCr0w/viral_meme_predictor",
      highlights: ["NLP + Computer Vision", "Reddit API Integration", "Face Detection", "Sentiment Analysis"],
      category: "Social Media Analytics"
    },
    {
      title: "Movie Recommendation System",
      description: "Intelligent movie recommendation engine using collaborative filtering and content-based approaches to suggest personalized movie recommendations.",
      icon: <Film className="w-8 h-8" />,
      technologies: ["Python", "Pandas", "NumPy", "Collaborative Filtering"],
      github: "https://github.com/LazyCr0w/movie_recommendation",
      highlights: ["Collaborative Filtering", "Content-Based Filtering", "Hybrid Approach", "Personalized Recommendations"],
      category: "Recommendation Systems"
    },
    {
      title: "LogBERT - Log Anomaly Detection",
      description: "Advanced log anomaly detection system using BERT transformer architecture to identify unusual patterns in system logs for cybersecurity applications.",
      icon: <Zap className="w-8 h-8" />,
      technologies: ["Python", "BERT", "Transformers", "PyTorch", "NLP"],
      github: "https://github.com/LazyCr0w/logbert",
      highlights: ["BERT Architecture", "Anomaly Detection", "Cybersecurity", "Deep Learning"],
      category: "Cybersecurity AI"
    },
    {
      title: "Weather Application",
      description: "Real-time weather application with clean UI and location-based forecasts. Features current conditions, hourly forecasts, and weather alerts.",
      icon: <Cloud className="w-8 h-8" />,
      technologies: ["HTML", "CSS", "JavaScript", "Weather API"],
      github: "https://github.com/LazyCr0w/Weather-App",
      highlights: ["Real-time Data", "Location-based", "Responsive Design", "Weather Alerts"],
      category: "Web Development"
    },
    {
      title: "QR Code Generator",
      description: "Simple and efficient QR code generator with customizable options for size, color, and error correction levels. Clean interface for easy use.",
      icon: <QrCode className="w-8 h-8" />,
      technologies: ["CSS", "JavaScript", "QR Library"],
      github: "https://github.com/LazyCr0w/QR-Generator",
      highlights: ["Customizable Options", "Multiple Formats", "Clean Interface", "Fast Generation"],
      category: "Utility Tools"
    }
  ];

  return (
    <section id="projects" className="py-20 relative">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            A showcase of my work in Data Science, Machine Learning, and Software Development. 
            Each project demonstrates different aspects of my technical expertise and problem-solving approach.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-400 group-hover:text-purple-400 transition-colors">
                    {project.icon}
                  </div>
                  <div>
                    <span className="text-xs text-gray-400 uppercase tracking-wide">
                      {project.category}
                    </span>
                    <h3 className="text-xl font-semibold text-white">
                      {project.title}
                    </h3>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 glass rounded-lg hover:bg-white/10 transition-all duration-300"
                  >
                    <Github className="w-5 h-5 text-gray-400 hover:text-white" />
                  </a>
                </div>
              </div>

              <p className="text-gray-300 mb-4 leading-relaxed">
                {project.description}
              </p>

              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-200 mb-2">Key Highlights:</h4>
                <div className="grid grid-cols-2 gap-2">
                  {project.highlights.map((highlight) => (
                    <div key={highlight} className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                      <span className="text-sm text-gray-300">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {project.technologies.map((tech) => (
                  <span
                    key={tech}
                    className="px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <a
            href="https://github.com/LazyCr0w"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 px-8 py-4 glass rounded-full hover:bg-white/10 transition-all duration-300"
          >
            <Github className="w-5 h-5" />
            <span>View All Projects on GitHub</span>
            <ExternalLink className="w-4 h-4" />
          </a>
        </motion.div>
      </div>
    </section>
  );
}
