'use client';

import { motion } from 'framer-motion';
import { Brain, Code, Database, TrendingUp } from 'lucide-react';
import Image from 'next/image';

export default function About() {
  const skills = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Machine Learning",
      description: "Expertise in classification, regression, ensemble methods, and deep learning",
      technologies: ["scikit-learn", "XGBoost", "TensorFlow", "PyTorch"]
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Data Science",
      description: "Data preprocessing, feature engineering, and statistical analysis",
      technologies: ["Pandas", "NumPy", "Matplotlib", "Seaborn"]
    },
    {
      icon: <Code className="w-8 h-8" />,
      title: "Programming",
      description: "Full-stack development with modern frameworks and tools",
      technologies: ["Python", "JavaScript", "React", "Next.js"]
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Analytics",
      description: "Business intelligence, data visualization, and predictive modeling",
      technologies: ["Streamlit", "Plotly", "Power BI", "SQL"]
    }
  ];

  return (
    <section id="about" className="py-20 relative">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 gradient-text">
            About Me
          </h2>

          {/* Profile Image */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-8 flex justify-center"
          >
            <div className="profile-image-container">
              <div className="w-48 h-48 md:w-56 md:h-56 rounded-full overflow-hidden glass glow relative z-10">
                <Image
                  src="/sumon-profile.jpg"
                  alt="Sumon Deb - Data Science & ML Engineer"
                  width={224}
                  height={224}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  priority
                />
              </div>
            </div>
          </motion.div>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            I'm a recent Computer Science graduate with a passion for transforming data into actionable insights.
            My journey in Data Science and Machine Learning has led me to develop innovative solutions across
            healthcare, entertainment, and business domains.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skills.map((skill, index) => (
            <motion.div
              key={skill.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group"
            >
              <div className="text-blue-400 mb-4 group-hover:text-purple-400 transition-colors">
                {skill.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-white">
                {skill.title}
              </h3>
              <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                {skill.description}
              </p>
              <div className="flex flex-wrap gap-2">
                {skill.technologies.map((tech) => (
                  <span
                    key={tech}
                    className="px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 glass rounded-2xl p-8"
        >
          <h3 className="text-2xl font-bold mb-6 text-center gradient-text">
            My Journey
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">🎓</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">Education</h4>
              <p className="text-gray-300 text-sm">
                Recent Computer Science graduate with focus on AI/ML and data analytics
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">🚀</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">Projects</h4>
              <p className="text-gray-300 text-sm">
                Built 6+ ML projects ranging from healthcare to entertainment analytics
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">🎯</span>
              </div>
              <h4 className="text-lg font-semibold mb-2">Goals</h4>
              <p className="text-gray-300 text-sm">
                Seeking opportunities to apply ML expertise in solving real-world challenges
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
