/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/About.tsx */ \"(rsc)/./src/components/About.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Contact.tsx */ \"(rsc)/./src/components/Contact.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(rsc)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(rsc)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(rsc)/./src/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Projects.tsx */ \"(rsc)/./src/components/Projects.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Skills.tsx */ \"(rsc)/./src/components/Skills.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2Rmcm9cXE9uZURyaXZlXFxEZXNrdG9wXFxQb3J0XFxwb3J0Zm9saW9cXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e30f610bf373\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNkZnJvXFxPbmVEcml2ZVxcRGVza3RvcFxcUG9ydFxccG9ydGZvbGlvXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMzBmNjEwYmYzNzNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Sumon Deb | Data Science & ML Portfolio\",\n    description: \"Portfolio of Sumon Deb, a recent CS graduate specializing in Data Science and Machine Learning. Showcasing projects in diabetes prediction, viral meme prediction, movie recommendations, and more.\",\n    keywords: [\n        \"Data Science\",\n        \"Machine Learning\",\n        \"Python\",\n        \"Portfolio\",\n        \"CS Graduate\",\n        \"AI\",\n        \"Analytics\",\n        \"Sumon Deb\"\n    ],\n    authors: [\n        {\n            name: \"Sumon Deb\"\n        }\n    ],\n    creator: \"Sumon Deb\",\n    openGraph: {\n        title: \"Sumon Deb | Data Science & ML Portfolio\",\n        description: \"Portfolio showcasing Data Science and Machine Learning projects by Sumon Deb\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-gray-900 text-white overflow-x-hidden`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_About__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/About */ \"(rsc)/./src/components/About.tsx\");\n/* harmony import */ var _components_Skills__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Skills */ \"(rsc)/./src/components/Skills.tsx\");\n/* harmony import */ var _components_Projects__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Projects */ \"(rsc)/./src/components/Projects.tsx\");\n/* harmony import */ var _components_Contact__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Contact */ \"(rsc)/./src/components/Contact.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"home\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_About__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Skills__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Projects__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Contact__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQWlEO0FBQ1o7QUFDRTtBQUNFO0FBQ0k7QUFDRjtBQUNGO0FBRTFCLFNBQVNPO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFLQyxXQUFVOzswQkFDZCw4REFBQ1QsOERBQVVBOzs7OzswQkFDWCw4REFBQ1U7Z0JBQUlDLElBQUc7MEJBQ04sNEVBQUNWLHdEQUFJQTs7Ozs7Ozs7OzswQkFFUCw4REFBQ0MseURBQUtBOzs7OzswQkFDTiw4REFBQ0MsMERBQU1BOzs7OzswQkFDUCw4REFBQ0MsNERBQVFBOzs7OzswQkFDVCw4REFBQ0MsMkRBQU9BOzs7OzswQkFDUiw4REFBQ0MsMERBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNkZnJvXFxPbmVEcml2ZVxcRGVza3RvcFxcUG9ydFxccG9ydGZvbGlvXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5hdmlnYXRpb24gZnJvbSAnQC9jb21wb25lbnRzL05hdmlnYXRpb24nO1xuaW1wb3J0IEhlcm8gZnJvbSAnQC9jb21wb25lbnRzL0hlcm8nO1xuaW1wb3J0IEFib3V0IGZyb20gJ0AvY29tcG9uZW50cy9BYm91dCc7XG5pbXBvcnQgU2tpbGxzIGZyb20gJ0AvY29tcG9uZW50cy9Ta2lsbHMnO1xuaW1wb3J0IFByb2plY3RzIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9qZWN0cyc7XG5pbXBvcnQgQ29udGFjdCBmcm9tICdAL2NvbXBvbmVudHMvQ29udGFjdCc7XG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAgPGRpdiBpZD1cImhvbWVcIj5cbiAgICAgICAgPEhlcm8gLz5cbiAgICAgIDwvZGl2PlxuICAgICAgPEFib3V0IC8+XG4gICAgICA8U2tpbGxzIC8+XG4gICAgICA8UHJvamVjdHMgLz5cbiAgICAgIDxDb250YWN0IC8+XG4gICAgICA8Rm9vdGVyIC8+XG4gICAgPC9tYWluPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIk5hdmlnYXRpb24iLCJIZXJvIiwiQWJvdXQiLCJTa2lsbHMiLCJQcm9qZWN0cyIsIkNvbnRhY3QiLCJGb290ZXIiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsImlkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/About.tsx":
/*!**********************************!*\
  !*** ./src/components/About.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\About.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Contact.tsx":
/*!************************************!*\
  !*** ./src/components/Contact.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Contact.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Hero.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Projects.tsx":
/*!*************************************!*\
  !*** ./src/components/Projects.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Projects.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/Skills.tsx":
/*!***********************************!*\
  !*** ./src/components/Skills.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Port\\portfolio\\src\\components\\Skills.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/About.tsx */ \"(ssr)/./src/components/About.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Contact.tsx */ \"(ssr)/./src/components/Contact.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(ssr)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Projects.tsx */ \"(ssr)/./src/components/Projects.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Skills.tsx */ \"(ssr)/./src/components/Skills.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CProjects.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csdfro%5C%5COneDrive%5C%5CDesktop%5C%5CPort%5C%5Cportfolio%5C%5Csrc%5C%5Ccomponents%5C%5CSkills.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/About.tsx":
/*!**********************************!*\
  !*** ./src/components/About.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ About)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Database,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Database,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Database,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Database,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction About() {\n    const skills = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            title: \"Machine Learning\",\n            description: \"Expertise in classification, regression, ensemble methods, and deep learning\",\n            technologies: [\n                \"scikit-learn\",\n                \"XGBoost\",\n                \"TensorFlow\",\n                \"PyTorch\"\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 16,\n                columnNumber: 13\n            }, this),\n            title: \"Data Science\",\n            description: \"Data preprocessing, feature engineering, and statistical analysis\",\n            technologies: [\n                \"Pandas\",\n                \"NumPy\",\n                \"Matplotlib\",\n                \"Seaborn\"\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            title: \"Programming\",\n            description: \"Full-stack development with modern frameworks and tools\",\n            technologies: [\n                \"Python\",\n                \"JavaScript\",\n                \"React\",\n                \"Next.js\"\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Database_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            title: \"Analytics\",\n            description: \"Business intelligence, data visualization, and predictive modeling\",\n            technologies: [\n                \"Streamlit\",\n                \"Plotly\",\n                \"Power BI\",\n                \"SQL\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        className: \"py-20 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 gradient-text\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mb-8 flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"profile-image-container\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-48 h-48 md:w-56 md:h-56 rounded-full overflow-hidden glass glow relative z-10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/sumon-profile.jpg\",\n                                        alt: \"Sumon Deb - Data Science & ML Engineer\",\n                                        width: 224,\n                                        height: 224,\n                                        className: \"w-full h-full object-cover hover:scale-105 transition-transform duration-300\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"I'm a recent Computer Science graduate with a passion for transforming data into actionable insights. My journey in Data Science and Machine Learning has led me to develop innovative solutions across healthcare, entertainment, and business domains.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-400 mb-4 group-hover:text-purple-400 transition-colors\",\n                                    children: skill.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-3 text-white\",\n                                    children: skill.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4 text-sm leading-relaxed\",\n                                    children: skill.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: skill.technologies.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700\",\n                                            children: tech\n                                        }, tech, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, skill.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 glass rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold mb-6 text-center gradient-text\",\n                            children: \"My Journey\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"\\uD83C\\uDF93\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Education\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Recent Computer Science graduate with focus on AI/ML and data analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Built 6+ ML projects ranging from healthcare to entertainment analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Goals\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Seeking opportunities to apply ML expertise in solving real-world challenges\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\About.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/About.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Contact.tsx":
/*!************************************!*\
  !*** ./src/components/Contact.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Mail,MapPin,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Contact() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n    });\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await fetch('https://formspree.io/f/xdkzzyyd', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    email: formData.email,\n                    subject: formData.subject,\n                    message: formData.message\n                })\n            });\n            if (response.ok) {\n                alert('Message sent successfully! I\\'ll get back to you soon.');\n                setFormData({\n                    name: '',\n                    email: '',\n                    subject: '',\n                    message: ''\n                });\n            } else {\n                alert('Failed to send message. Please try again or contact me directly.');\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            alert('Failed to send message. Please try again or contact me directly.');\n        }\n    };\n    const contactInfo = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                lineNumber: 53,\n                columnNumber: 13\n            }, this),\n            title: \"Email\",\n            value: \"<EMAIL>\",\n            link: \"mailto:<EMAIL>\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                lineNumber: 59,\n                columnNumber: 13\n            }, this),\n            title: \"GitHub\",\n            value: \"github.com/LazyCr0w\",\n            link: \"https://github.com/LazyCr0w\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            title: \"LinkedIn\",\n            value: \"linkedin.com/in/sumon-deb-742346314\",\n            link: \"https://www.linkedin.com/in/sumon-deb-742346314\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            title: \"Location\",\n            value: \"Available for Remote Work\",\n            link: null\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        className: \"py-20 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 gradient-text\",\n                            children: \"Get In Touch\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"I'm always interested in discussing new opportunities, collaborations, or just chatting about Data Science and Machine Learning. Feel free to reach out!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold mb-6 gradient-text\",\n                                            children: \"Let's Connect\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-8 leading-relaxed\",\n                                            children: \"Whether you're looking for a Data Scientist, have a project in mind, or want to discuss the latest in ML research, I'd love to hear from you. I'm currently seeking opportunities to apply my skills in a professional environment.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"flex items-center space-x-4 p-4 glass rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400\",\n                                                    children: info.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-white font-semibold\",\n                                                            children: info.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        info.link ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: info.link,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-gray-300 hover:text-blue-400 transition-colors\",\n                                                            children: info.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300\",\n                                                            children: info.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, info.title, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"glass rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-4 gradient-text\",\n                                            children: \"What I'm Looking For\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Data Scientist positions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Machine Learning Engineer roles\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-cyan-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Research collaborations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Freelance ML projects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"glass rounded-2xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-6 gradient-text\",\n                                    children: \"Send a Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"name\",\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                            children: \"Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"name\",\n                                                            name: \"name\",\n                                                            value: formData.name,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\",\n                                                            placeholder: \"Your Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"email\",\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\",\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"subject\",\n                                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                    children: \"Subject\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"subject\",\n                                                    name: \"subject\",\n                                                    value: formData.subject,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\",\n                                                    placeholder: \"What's this about?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                    children: \"Message\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    value: formData.message,\n                                                    onChange: handleChange,\n                                                    required: true,\n                                                    rows: 6,\n                                                    className: \"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none\",\n                                                    placeholder: \"Tell me about your project or opportunity...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                            type: \"submit\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 glow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Mail_MapPin_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Send Message\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Contact.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Contact.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const socialLinks = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this),\n            href: \"https://github.com/LazyCr0w\",\n            label: \"GitHub\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 16,\n                columnNumber: 13\n            }, this),\n            href: \"https://www.linkedin.com/in/sumon-deb-742346314\",\n            label: \"LinkedIn\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-5 h-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 21,\n                columnNumber: 13\n            }, this),\n            href: \"mailto:<EMAIL>\",\n            label: \"Email\"\n        }\n    ];\n    const quickLinks = [\n        {\n            name: 'About',\n            href: '#about'\n        },\n        {\n            name: 'Skills',\n            href: '#skills'\n        },\n        {\n            name: 'Projects',\n            href: '#projects'\n        },\n        {\n            name: 'Contact',\n            href: '#contact'\n        }\n    ];\n    const handleNavClick = (href)=>{\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative py-12 border-t border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold gradient-text\",\n                                        children: \"Sumon Deb\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 leading-relaxed\",\n                                        children: \"Data Science & Machine Learning enthusiast passionate about creating intelligent solutions that make a real-world impact.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                                href: link.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: index * 0.1\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\",\n                                                \"aria-label\": link.label,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 group-hover:text-blue-400 transition-colors\",\n                                                    children: link.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, link.label, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2\",\n                                        children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleNavClick(link.href),\n                                                    className: \"text-gray-300 hover:text-blue-400 transition-colors duration-300\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Get In Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Available for remote opportunities\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Open to collaborations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Always learning something new\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"glass rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mb-2\",\n                                                children: \"Currently exploring:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-xs\",\n                                                        children: \"Deep Learning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-xs\",\n                                                        children: \"MLOps\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded text-xs\",\n                                                        children: \"Computer Vision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"mt-12 pt-8 border-t border-gray-800 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" Sumon Deb. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Built with\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"using Next.js & Tailwind CSS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"min-h-screen flex items-center justify-center relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 text-center relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.5\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"text-6xl md:text-8xl font-bold mb-6 gradient-text\",\n                            children: \"Sumon Deb\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed\",\n                            children: [\n                                \"Recent CS Graduate specializing in\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-400 font-semibold\",\n                                    children: \"Data Science\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                \" &\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-purple-400 font-semibold\",\n                                    children: \"Machine Learning\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            className: \"text-lg text-gray-400 mb-12 max-w-2xl mx-auto\",\n                            children: \"Passionate about building intelligent systems that solve real-world problems. From healthcare predictions to viral content analysis, I create ML solutions that make an impact.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.8\n                            },\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#projects\",\n                                    className: \"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 glow\",\n                                    children: \"View My Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#contact\",\n                                    className: \"px-8 py-4 glass rounded-full text-white font-semibold hover:bg-white/10 transition-all duration-300\",\n                                    children: \"Get In Touch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1.0\n                            },\n                            className: \"flex justify-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com/LazyCr0w\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6 group-hover:text-blue-400 transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 group-hover:text-blue-400 transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://www.linkedin.com/in/sumon-deb-742346314\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 group-hover:text-blue-400 transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1,\n                    delay: 1.5\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    className: \"flex flex-col items-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm mb-2\",\n                            children: \"Scroll to explore\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navigation() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    const navItems = [\n        {\n            name: 'Home',\n            href: '#home'\n        },\n        {\n            name: 'About',\n            href: '#about'\n        },\n        {\n            name: 'Skills',\n            href: '#skills'\n        },\n        {\n            name: 'Projects',\n            href: '#projects'\n        },\n        {\n            name: 'Contact',\n            href: '#contact'\n        }\n    ];\n    const handleNavClick = (href)=>{\n        setIsOpen(false);\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.nav, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"text-2xl font-bold gradient-text cursor-pointer\",\n                            onClick: ()=>handleNavClick('#home'),\n                            children: \"Sumon Deb\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4 + index * 0.1\n                                    },\n                                    onClick: ()=>handleNavClick(item.href),\n                                    className: \"text-gray-300 hover:text-white transition-colors duration-300 relative group\",\n                                    children: [\n                                        item.name,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            onClick: ()=>setIsOpen(!isOpen),\n                            className: \"md:hidden p-2 text-gray-300 hover:text-white transition-colors\",\n                            children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 23\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 51\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: isOpen ? 1 : 0,\n                        height: isOpen ? 'auto' : 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"md:hidden overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4 space-y-4\",\n                        children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: isOpen ? 1 : 0,\n                                    x: isOpen ? 0 : -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: index * 0.1\n                                },\n                                onClick: ()=>handleNavClick(item.href),\n                                className: \"block w-full text-left text-gray-300 hover:text-white transition-colors duration-300 py-2\",\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Projects.tsx":
/*!*************************************!*\
  !*** ./src/components/Projects.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Projects)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/film.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Cloud,ExternalLink,Film,Github,Heart,QrCode,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Projects() {\n    const projects = [\n        {\n            title: \"Diabetes Prediction ML Pipeline\",\n            description: \"Comprehensive machine learning pipeline for diabetes prediction using ensemble methods with 82.04% ROC AUC. Features advanced preprocessing, feature engineering, and interactive Streamlit web interface.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"Python\",\n                \"scikit-learn\",\n                \"XGBoost\",\n                \"Streamlit\",\n                \"SMOTE\"\n            ],\n            github: \"https://github.com/LazyCr0w/Diabetes_Prediction\",\n            highlights: [\n                \"82.04% ROC AUC\",\n                \"5 ML Models Ensemble\",\n                \"25+ Visualizations\",\n                \"Interactive Web App\"\n            ],\n            category: \"Healthcare ML\"\n        },\n        {\n            title: \"Viral Meme Predictor\",\n            description: \"Machine learning application that predicts meme virality by analyzing both text and image features. Combines NLP and computer vision to extract meaningful patterns from Reddit data.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"Python\",\n                \"OpenCV\",\n                \"TextBlob\",\n                \"PRAW\",\n                \"scikit-learn\"\n            ],\n            github: \"https://github.com/LazyCr0w/viral_meme_predictor\",\n            highlights: [\n                \"NLP + Computer Vision\",\n                \"Reddit API Integration\",\n                \"Face Detection\",\n                \"Sentiment Analysis\"\n            ],\n            category: \"Social Media Analytics\"\n        },\n        {\n            title: \"Movie Recommendation System\",\n            description: \"Intelligent movie recommendation engine using collaborative filtering and content-based approaches to suggest personalized movie recommendations.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 29,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"Python\",\n                \"Pandas\",\n                \"NumPy\",\n                \"Collaborative Filtering\"\n            ],\n            github: \"https://github.com/LazyCr0w/movie_recommendation\",\n            highlights: [\n                \"Collaborative Filtering\",\n                \"Content-Based Filtering\",\n                \"Hybrid Approach\",\n                \"Personalized Recommendations\"\n            ],\n            category: \"Recommendation Systems\"\n        },\n        {\n            title: \"LogBERT - Log Anomaly Detection\",\n            description: \"Advanced log anomaly detection system using BERT transformer architecture to identify unusual patterns in system logs for cybersecurity applications.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"Python\",\n                \"BERT\",\n                \"Transformers\",\n                \"PyTorch\",\n                \"NLP\"\n            ],\n            github: \"https://github.com/LazyCr0w/logbert\",\n            highlights: [\n                \"BERT Architecture\",\n                \"Anomaly Detection\",\n                \"Cybersecurity\",\n                \"Deep Learning\"\n            ],\n            category: \"Cybersecurity AI\"\n        },\n        {\n            title: \"Weather Application\",\n            description: \"Real-time weather application with clean UI and location-based forecasts. Features current conditions, hourly forecasts, and weather alerts.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 47,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"HTML\",\n                \"CSS\",\n                \"JavaScript\",\n                \"Weather API\"\n            ],\n            github: \"https://github.com/LazyCr0w/Weather-App\",\n            highlights: [\n                \"Real-time Data\",\n                \"Location-based\",\n                \"Responsive Design\",\n                \"Weather Alerts\"\n            ],\n            category: \"Web Development\"\n        },\n        {\n            title: \"QR Code Generator\",\n            description: \"Simple and efficient QR code generator with customizable options for size, color, and error correction levels. Clean interface for easy use.\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-8 h-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                lineNumber: 56,\n                columnNumber: 13\n            }, this),\n            technologies: [\n                \"CSS\",\n                \"JavaScript\",\n                \"QR Library\"\n            ],\n            github: \"https://github.com/LazyCr0w/QR-Generator\",\n            highlights: [\n                \"Customizable Options\",\n                \"Multiple Formats\",\n                \"Clean Interface\",\n                \"Fast Generation\"\n            ],\n            category: \"Utility Tools\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        className: \"py-20 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 gradient-text\",\n                            children: \"Featured Projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"A showcase of my work in Data Science, Machine Learning, and Software Development. Each project demonstrates different aspects of my technical expertise and problem-solving approach.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8\",\n                    children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-400 group-hover:text-purple-400 transition-colors\",\n                                                    children: project.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400 uppercase tracking-wide\",\n                                                            children: project.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-white\",\n                                                            children: project.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: project.github,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"p-2 glass rounded-lg hover:bg-white/10 transition-all duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-400 hover:text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-4 leading-relaxed\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-semibold text-gray-200 mb-2\",\n                                            children: \"Key Highlights:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2\",\n                                            children: project.highlights.map((highlight)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1.5 h-1.5 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: highlight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, highlight, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: project.technologies.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700\",\n                                            children: tech\n                                        }, tech, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, project.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mt-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"https://github.com/LazyCr0w\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"inline-flex items-center space-x-2 px-8 py-4 glass rounded-full hover:bg-white/10 transition-all duration-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"View All Projects on GitHub\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Cloud_ExternalLink_Film_Github_Heart_QrCode_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Projects.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Projects.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Skills.tsx":
/*!***********************************!*\
  !*** ./src/components/Skills.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Skills)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Skills() {\n    const skillCategories = [\n        {\n            title: \"Machine Learning & AI\",\n            skills: [\n                {\n                    name: \"Python\",\n                    level: 95\n                },\n                {\n                    name: \"scikit-learn\",\n                    level: 90\n                },\n                {\n                    name: \"TensorFlow\",\n                    level: 85\n                },\n                {\n                    name: \"PyTorch\",\n                    level: 80\n                },\n                {\n                    name: \"XGBoost\",\n                    level: 88\n                },\n                {\n                    name: \"BERT/Transformers\",\n                    level: 75\n                }\n            ]\n        },\n        {\n            title: \"Data Science & Analytics\",\n            skills: [\n                {\n                    name: \"Pandas\",\n                    level: 92\n                },\n                {\n                    name: \"NumPy\",\n                    level: 90\n                },\n                {\n                    name: \"Matplotlib\",\n                    level: 85\n                },\n                {\n                    name: \"Seaborn\",\n                    level: 85\n                },\n                {\n                    name: \"Plotly\",\n                    level: 80\n                },\n                {\n                    name: \"SQL\",\n                    level: 82\n                }\n            ]\n        },\n        {\n            title: \"Web Development\",\n            skills: [\n                {\n                    name: \"JavaScript\",\n                    level: 85\n                },\n                {\n                    name: \"React\",\n                    level: 80\n                },\n                {\n                    name: \"Next.js\",\n                    level: 78\n                },\n                {\n                    name: \"HTML/CSS\",\n                    level: 88\n                },\n                {\n                    name: \"Streamlit\",\n                    level: 90\n                },\n                {\n                    name: \"Node.js\",\n                    level: 75\n                }\n            ]\n        },\n        {\n            title: \"Tools & Technologies\",\n            skills: [\n                {\n                    name: \"Git/GitHub\",\n                    level: 88\n                },\n                {\n                    name: \"Docker\",\n                    level: 70\n                },\n                {\n                    name: \"Jupyter\",\n                    level: 92\n                },\n                {\n                    name: \"VS Code\",\n                    level: 90\n                },\n                {\n                    name: \"Linux\",\n                    level: 75\n                },\n                {\n                    name: \"AWS\",\n                    level: 65\n                }\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        className: \"py-20 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold mb-6 gradient-text\",\n                            children: \"Technical Skills\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"A comprehensive overview of my technical expertise across different domains. These skills have been developed through academic projects, personal learning, and hands-on experience.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: skillCategories.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: categoryIndex % 2 === 0 ? -50 : 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: categoryIndex * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"glass rounded-2xl p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-6 text-center gradient-text\",\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: category.skills.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: categoryIndex * 0.1 + skillIndex * 0.05\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: skill.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: [\n                                                                skill.level,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-800 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        whileInView: {\n                                                            width: `${skill.level}%`\n                                                        },\n                                                        transition: {\n                                                            duration: 1,\n                                                            delay: categoryIndex * 0.1 + skillIndex * 0.05 + 0.3\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, skill.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, category.title, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 glass rounded-2xl p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold mb-6 text-center gradient-text\",\n                            children: \"Certifications & Learning\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83C\\uDFC6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Academic Excellence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Strong foundation in Computer Science with focus on AI/ML coursework\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDCDA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Continuous Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Actively pursuing online courses and staying updated with latest ML trends\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDD2C\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"Research Oriented\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 text-sm\",\n                                            children: \"Passionate about exploring new algorithms and contributing to open source\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Port\\\\portfolio\\\\src\\\\components\\\\Skills.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Skills.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csdfro%5COneDrive%5CDesktop%5CPort%5Cportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();