import type { Metadata } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "LazyCr0w | Data Science & ML Portfolio",
  description: "<PERSON><PERSON><PERSON> of a recent CS graduate specializing in Data Science and Machine Learning. Showcasing projects in diabetes prediction, viral meme prediction, movie recommendations, and more.",
  keywords: ["Data Science", "Machine Learning", "Python", "Portfolio", "CS Graduate", "AI", "Analytics"],
  authors: [{ name: "LazyCr0w" }],
  creator: "LazyCr0w",
  openGraph: {
    title: "LazyCr0w | Data Science & ML Portfolio",
    description: "Portfolio showcasing Data Science and Machine Learning projects",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased bg-gray-900 text-white overflow-x-hidden`}
      >
        {children}
      </body>
    </html>
  );
}
