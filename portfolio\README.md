# LazyCr0w Portfolio 🚀

A sleek and modern portfolio website showcasing Data Science and Machine Learning projects. Built with Next.js, TypeScript, and Tailwind CSS with beautiful animations and a materialistic design.

![Portfolio Preview](https://img.shields.io/badge/Status-Live-brightgreen)
![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-4.0-38bdf8)

## ✨ Features

- **🎨 Modern Design**: Sleek materialistic design with glass morphism effects
- **🌟 Smooth Animations**: Beautiful animations powered by Framer Motion
- **📱 Responsive**: Fully responsive design that works on all devices
- **⚡ Fast Performance**: Built with Next.js 15 and optimized for speed
- **🎯 Project Showcase**: Dedicated sections for ML/DS projects with detailed descriptions
- **📊 Skills Visualization**: Interactive skill bars and progress indicators
- **📧 Contact Form**: Functional contact form for inquiries
- **🔗 Social Integration**: Links to GitHub, LinkedIn, and email

## 🛠️ Tech Stack

- **Framework**: Next.js 15.3.3
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4.0
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Inter & JetBrains Mono
- **Deployment**: Ready for Vercel

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/LazyCr0w/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
portfolio/
├── src/
│   ├── app/
│   │   ├── globals.css      # Global styles and animations
│   │   ├── layout.tsx       # Root layout with metadata
│   │   └── page.tsx         # Main page component
│   └── components/
│       ├── Hero.tsx         # Landing section with intro
│       ├── About.tsx        # About me section
│       ├── Skills.tsx       # Technical skills with progress bars
│       ├── Projects.tsx     # Project showcase
│       ├── Contact.tsx      # Contact form and info
│       ├── Navigation.tsx   # Responsive navigation
│       └── Footer.tsx       # Footer with social links
├── public/                  # Static assets
├── package.json
└── README.md
```

## 🎯 Featured Projects

### 🏥 Diabetes Prediction ML Pipeline
- **82.04% ROC AUC** with ensemble methods
- Interactive Streamlit web interface
- Advanced feature engineering with 22+ features
- **Tech**: Python, scikit-learn, XGBoost, Streamlit

### 🧠 Viral Meme Predictor
- NLP + Computer Vision analysis
- Reddit API integration for data collection
- Face detection and sentiment analysis
- **Tech**: Python, OpenCV, TextBlob, PRAW

### 🎬 Movie Recommendation System
- Collaborative and content-based filtering
- Hybrid recommendation approach
- **Tech**: Python, Pandas, NumPy

### ⚡ LogBERT - Log Anomaly Detection
- BERT transformer architecture
- Cybersecurity applications
- **Tech**: Python, BERT, PyTorch, Transformers

## 🎨 Design Features

- **Glass Morphism**: Translucent elements with backdrop blur
- **Gradient Animations**: Dynamic color transitions
- **Smooth Scrolling**: Seamless navigation between sections
- **Hover Effects**: Interactive elements with visual feedback
- **Dark Theme**: Modern dark color scheme with accent colors
- **Typography**: Clean typography with Inter and JetBrains Mono

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Tablet Support**: Perfect layout for tablets
- **Desktop Enhanced**: Rich experience on larger screens
- **Cross Browser**: Compatible with all modern browsers

## 🚀 Deployment

### Deploy on Vercel (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy on Vercel**
   - Visit [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Deploy with one click

### Other Deployment Options

- **Netlify**: Connect GitHub repo for automatic deployments
- **AWS Amplify**: Full-stack deployment with CI/CD
- **Docker**: Containerized deployment for any platform

## 🔧 Customization

### Update Personal Information

1. **Contact Details**: Edit `src/components/Contact.tsx` and `src/components/Footer.tsx`
2. **Social Links**: Update URLs in `src/components/Hero.tsx` and `src/components/Footer.tsx`
3. **Projects**: Modify project data in `src/components/Projects.tsx`
4. **Skills**: Update skill levels in `src/components/Skills.tsx`

### Styling Customization

- **Colors**: Modify CSS variables in `src/app/globals.css`
- **Fonts**: Change font imports in `src/app/layout.tsx`
- **Animations**: Adjust Framer Motion settings in components

## 📊 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for excellent user experience
- **Bundle Size**: Minimized with Next.js optimization
- **Loading Speed**: Fast initial page load and navigation

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

**LazyCr0w** - [GitHub](https://github.com/LazyCr0w)

**Project Link**: [https://github.com/LazyCr0w/portfolio](https://github.com/LazyCr0w/portfolio)

---

⭐ **Star this repository if you found it helpful!** ⭐
