{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\nimport { Menu, X } from 'lucide-react';\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const handleNavClick = (href: string) => {\n    setIsOpen(false);\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled ? 'glass backdrop-blur-md' : 'bg-transparent'\n      }`}\n    >\n      <div className=\"container mx-auto px-6\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-2xl font-bold gradient-text cursor-pointer\"\n            onClick={() => handleNavClick('#home')}\n          >\n            Sumon Deb\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"hidden md:flex items-center space-x-8\"\n          >\n            {navItems.map((item, index) => (\n              <motion.button\n                key={item.name}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 + index * 0.1 }}\n                onClick={() => handleNavClick(item.href)}\n                className=\"text-gray-300 hover:text-white transition-colors duration-300 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300\"></span>\n              </motion.button>\n            ))}\n          </motion.div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 text-gray-300 hover:text-white transition-colors\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </motion.button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{\n            opacity: isOpen ? 1 : 0,\n            height: isOpen ? 'auto' : 0\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"md:hidden overflow-hidden\"\n        >\n          <div className=\"py-4 space-y-4\">\n            {navItems.map((item, index) => (\n              <motion.button\n                key={item.name}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{\n                  opacity: isOpen ? 1 : 0,\n                  x: isOpen ? 0 : -20\n                }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n                onClick={() => handleNavClick(item.href)}\n                className=\"block w-full text-left text-gray-300 hover:text-white transition-colors duration-300 py-2\"\n              >\n                {item.name}\n              </motion.button>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,iBAAiB,CAAC;QACtB,UAAU;QACV,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,4DAA4D,EACtE,WAAW,2BAA2B,kBACtC;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;4BACV,SAAS,IAAM,eAAe;sCAC/B;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAET,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,SAAS,IAAM,eAAe,KAAK,IAAI;oCACvC,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;4CAAK,WAAU;;;;;;;mCARX,KAAK,IAAI;;;;;;;;;;sCAcpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBACP,SAAS,SAAS,IAAI;wBACtB,QAAQ,SAAS,SAAS;oBAC5B;oBACA,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCACP,SAAS,SAAS,IAAI;oCACtB,GAAG,SAAS,IAAI,CAAC;gCACnB;gCACA,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,SAAS,IAAM,eAAe,KAAK,IAAI;gCACvC,WAAU;0CAET,KAAK,IAAI;+BAVL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Github, Linkedin, Mail } from 'lucide-react';\n\nexport default function Hero() {\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-2000\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-6 text-center relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"max-w-4xl mx-auto\"\n        >\n          <motion.h1\n            initial={{ opacity: 0, scale: 0.5 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"text-6xl md:text-8xl font-bold mb-6 gradient-text\"\n          >\n            Sumon Deb\n          </motion.h1>\n          \n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed\"\n          >\n            Recent CS Graduate specializing in{' '}\n            <span className=\"text-blue-400 font-semibold\">Data Science</span> &{' '}\n            <span className=\"text-purple-400 font-semibold\">Machine Learning</span>\n          </motion.p>\n\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            className=\"text-lg text-gray-400 mb-12 max-w-2xl mx-auto\"\n          >\n            Passionate about building intelligent systems that solve real-world problems. \n            From healthcare predictions to viral content analysis, I create ML solutions that make an impact.\n          </motion.p>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <a\n              href=\"#projects\"\n              className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 glow\"\n            >\n              View My Projects\n            </a>\n            <a\n              href=\"#contact\"\n              className=\"px-8 py-4 glass rounded-full text-white font-semibold hover:bg-white/10 transition-all duration-300\"\n            >\n              Get In Touch\n            </a>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.0 }}\n            className=\"flex justify-center space-x-6\"\n          >\n            <a\n              href=\"https://github.com/LazyCr0w\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\"\n            >\n              <Github className=\"w-6 h-6 group-hover:text-blue-400 transition-colors\" />\n            </a>\n            <a\n              href=\"mailto:<EMAIL>\"\n              className=\"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\"\n            >\n              <Mail className=\"w-6 h-6 group-hover:text-blue-400 transition-colors\" />\n            </a>\n            <a\n              href=\"https://www.linkedin.com/in/sumon-deb-*********\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\"\n            >\n              <Linkedin className=\"w-6 h-6 group-hover:text-blue-400 transition-colors\" />\n            </a>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"flex flex-col items-center text-gray-400\"\n        >\n          <span className=\"text-sm mb-2\">Scroll to explore</span>\n          <ChevronDown className=\"w-6 h-6\" />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;gCACoC;8CACnC,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;gCAAmB;gCAAG;8CACpE,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAGlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAe;;;;;;sCAC/B,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKjC", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Brain, Code, Database, TrendingUp } from 'lucide-react';\n\nexport default function About() {\n  const skills = [\n    {\n      icon: <Brain className=\"w-8 h-8\" />,\n      title: \"Machine Learning\",\n      description: \"Expertise in classification, regression, ensemble methods, and deep learning\",\n      technologies: [\"scikit-learn\", \"XGBoost\", \"TensorFlow\", \"PyTorch\"]\n    },\n    {\n      icon: <Database className=\"w-8 h-8\" />,\n      title: \"Data Science\",\n      description: \"Data preprocessing, feature engineering, and statistical analysis\",\n      technologies: [\"Pandas\", \"NumPy\", \"Matplotlib\", \"Seaborn\"]\n    },\n    {\n      icon: <Code className=\"w-8 h-8\" />,\n      title: \"Programming\",\n      description: \"Full-stack development with modern frameworks and tools\",\n      technologies: [\"Python\", \"JavaScript\", \"React\", \"Next.js\"]\n    },\n    {\n      icon: <TrendingUp className=\"w-8 h-8\" />,\n      title: \"Analytics\",\n      description: \"Business intelligence, data visualization, and predictive modeling\",\n      technologies: [\"Streamlit\", \"Plotly\", \"Power BI\", \"SQL\"]\n    }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gradient-text\">\n            About Me\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            I'm a recent Computer Science graduate with a passion for transforming data into actionable insights. \n            My journey in Data Science and Machine Learning has led me to develop innovative solutions across \n            healthcare, entertainment, and business domains.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {skills.map((skill, index) => (\n            <motion.div\n              key={skill.title}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group\"\n            >\n              <div className=\"text-blue-400 mb-4 group-hover:text-purple-400 transition-colors\">\n                {skill.icon}\n              </div>\n              <h3 className=\"text-xl font-semibold mb-3 text-white\">\n                {skill.title}\n              </h3>\n              <p className=\"text-gray-300 mb-4 text-sm leading-relaxed\">\n                {skill.description}\n              </p>\n              <div className=\"flex flex-wrap gap-2\">\n                {skill.technologies.map((tech) => (\n                  <span\n                    key={tech}\n                    className=\"px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700\"\n                  >\n                    {tech}\n                  </span>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"mt-16 glass rounded-2xl p-8\"\n        >\n          <h3 className=\"text-2xl font-bold mb-6 text-center gradient-text\">\n            My Journey\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl font-bold\">🎓</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Education</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Recent Computer Science graduate with focus on AI/ML and data analytics\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl font-bold\">🚀</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Projects</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Built 6+ ML projects ranging from healthcare to entertainment analytics\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl font-bold\">🎯</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Goals</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Seeking opportunities to apply ML expertise in solving real-world challenges\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAgB;gBAAW;gBAAc;aAAU;QACpE;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAU;gBAAS;gBAAc;aAAU;QAC5D;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAU;gBAAc;gBAAS;aAAU;QAC5D;QACA;YACE,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAa;gBAAU;gBAAY;aAAM;QAC1D;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI;;;;;;8CAEb,8OAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;8CAEpB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;;2BAnBN,MAAM,KAAK;;;;;;;;;;8BA8BtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function Skills() {\n  const skillCategories = [\n    {\n      title: \"Machine Learning & AI\",\n      skills: [\n        { name: \"Python\", level: 95 },\n        { name: \"scikit-learn\", level: 90 },\n        { name: \"Tensor<PERSON><PERSON>\", level: 85 },\n        { name: \"<PERSON>yTor<PERSON>\", level: 80 },\n        { name: \"XGBoost\", level: 88 },\n        { name: \"BERT/Transformers\", level: 75 }\n      ]\n    },\n    {\n      title: \"Data Science & Analytics\",\n      skills: [\n        { name: \"Pandas\", level: 92 },\n        { name: \"NumPy\", level: 90 },\n        { name: \"Matplotlib\", level: 85 },\n        { name: \"<PERSON>born\", level: 85 },\n        { name: \"<PERSON>lotly\", level: 80 },\n        { name: \"SQL\", level: 82 }\n      ]\n    },\n    {\n      title: \"Web Development\",\n      skills: [\n        { name: \"JavaScript\", level: 85 },\n        { name: \"React\", level: 80 },\n        { name: \"Next.js\", level: 78 },\n        { name: \"HTML/CSS\", level: 88 },\n        { name: \"Streamlit\", level: 90 },\n        { name: \"Node.js\", level: 75 }\n      ]\n    },\n    {\n      title: \"Tools & Technologies\",\n      skills: [\n        { name: \"Git/GitHub\", level: 88 },\n        { name: \"Docker\", level: 70 },\n        { name: \"Jupyter\", level: 92 },\n        { name: \"VS Code\", level: 90 },\n        { name: \"Linux\", level: 75 },\n        { name: \"AWS\", level: 65 }\n      ]\n    }\n  ];\n\n  return (\n    <section id=\"skills\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gradient-text\">\n            Technical Skills\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            A comprehensive overview of my technical expertise across different domains. \n            These skills have been developed through academic projects, personal learning, and hands-on experience.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              initial={{ opacity: 0, x: categoryIndex % 2 === 0 ? -50 : 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}\n              viewport={{ once: true }}\n              className=\"glass rounded-2xl p-6\"\n            >\n              <h3 className=\"text-2xl font-bold mb-6 text-center gradient-text\">\n                {category.title}\n              </h3>\n              <div className=\"space-y-4\">\n                {category.skills.map((skill, skillIndex) => (\n                  <motion.div\n                    key={skill.name}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}\n                    viewport={{ once: true }}\n                    className=\"space-y-2\"\n                  >\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-white font-medium\">{skill.name}</span>\n                      <span className=\"text-gray-400 text-sm\">{skill.level}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-800 rounded-full h-2\">\n                      <motion.div\n                        initial={{ width: 0 }}\n                        whileInView={{ width: `${skill.level}%` }}\n                        transition={{ duration: 1, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) + 0.3 }}\n                        viewport={{ once: true }}\n                        className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full\"\n                      />\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"mt-16 glass rounded-2xl p-8\"\n        >\n          <h3 className=\"text-2xl font-bold mb-6 text-center gradient-text\">\n            Certifications & Learning\n          </h3>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">🏆</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Academic Excellence</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Strong foundation in Computer Science with focus on AI/ML coursework\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Continuous Learning</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Actively pursuing online courses and staying updated with latest ML trends\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">🔬</span>\n              </div>\n              <h4 className=\"text-lg font-semibold mb-2\">Research Oriented</h4>\n              <p className=\"text-gray-300 text-sm\">\n                Passionate about exploring new algorithms and contributing to open source\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAgB,OAAO;gBAAG;gBAClC;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAqB,OAAO;gBAAG;aACxC;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAS,OAAO;gBAAG;gBAC3B;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAO,OAAO;gBAAG;aAC1B;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAS,OAAO;gBAAG;gBAC3B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAY,OAAO;gBAAG;gBAC9B;oBAAE,MAAM;oBAAa,OAAO;gBAAG;gBAC/B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;aAC9B;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;oBAAE,MAAM;oBAAc,OAAO;gBAAG;gBAChC;oBAAE,MAAM;oBAAU,OAAO;gBAAG;gBAC5B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAW,OAAO;gBAAG;gBAC7B;oBAAE,MAAM;oBAAS,OAAO;gBAAG;gBAC3B;oBAAE,MAAM;oBAAO,OAAO;gBAAG;aAC1B;QACH;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG,gBAAgB,MAAM,IAAI,CAAC,KAAK;4BAAG;4BAC7D,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,gBAAgB;4BAAI;4BACxD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,AAAC,gBAAgB,MAAQ,aAAa;4CAAM;4CAChF,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAA0B,MAAM,IAAI;;;;;;sEACpD,8OAAC;4DAAK,WAAU;;gEAAyB,MAAM,KAAK;gEAAC;;;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;wDAAE;wDACpB,aAAa;4DAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;wDAAC;wDACxC,YAAY;4DAAE,UAAU;4DAAG,OAAO,AAAC,gBAAgB,MAAQ,aAAa,OAAQ;wDAAI;wDACpF,UAAU;4DAAE,MAAM;wDAAK;wDACvB,WAAU;;;;;;;;;;;;2CAjBT,MAAM,IAAI;;;;;;;;;;;2BAbhB,SAAS,KAAK;;;;;;;;;;8BAwCzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Projects.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ExternalLink, Github, Heart, Brain, Film, Zap, Cloud, QrCode } from 'lucide-react';\n\nexport default function Projects() {\n  const projects = [\n    {\n      title: \"Diabetes Prediction ML Pipeline\",\n      description: \"Comprehensive machine learning pipeline for diabetes prediction using ensemble methods with 82.04% ROC AUC. Features advanced preprocessing, feature engineering, and interactive Streamlit web interface.\",\n      icon: <Heart className=\"w-8 h-8\" />,\n      technologies: [\"Python\", \"scikit-learn\", \"XGBoost\", \"Streamlit\", \"SMOTE\"],\n      github: \"https://github.com/LazyCr0w/Diabetes_Prediction\",\n      highlights: [\"82.04% ROC AUC\", \"5 ML Models Ensemble\", \"25+ Visualizations\", \"Interactive Web App\"],\n      category: \"Healthcare ML\"\n    },\n    {\n      title: \"Viral Meme Predictor\",\n      description: \"Machine learning application that predicts meme virality by analyzing both text and image features. Combines NLP and computer vision to extract meaningful patterns from Reddit data.\",\n      icon: <Brain className=\"w-8 h-8\" />,\n      technologies: [\"Python\", \"OpenCV\", \"TextBlob\", \"PRAW\", \"scikit-learn\"],\n      github: \"https://github.com/LazyCr0w/viral_meme_predictor\",\n      highlights: [\"NLP + Computer Vision\", \"Reddit API Integration\", \"Face Detection\", \"Sentiment Analysis\"],\n      category: \"Social Media Analytics\"\n    },\n    {\n      title: \"Movie Recommendation System\",\n      description: \"Intelligent movie recommendation engine using collaborative filtering and content-based approaches to suggest personalized movie recommendations.\",\n      icon: <Film className=\"w-8 h-8\" />,\n      technologies: [\"Python\", \"Pandas\", \"NumPy\", \"Collaborative Filtering\"],\n      github: \"https://github.com/LazyCr0w/movie_recommendation\",\n      highlights: [\"Collaborative Filtering\", \"Content-Based Filtering\", \"Hybrid Approach\", \"Personalized Recommendations\"],\n      category: \"Recommendation Systems\"\n    },\n    {\n      title: \"LogBERT - Log Anomaly Detection\",\n      description: \"Advanced log anomaly detection system using BERT transformer architecture to identify unusual patterns in system logs for cybersecurity applications.\",\n      icon: <Zap className=\"w-8 h-8\" />,\n      technologies: [\"Python\", \"BERT\", \"Transformers\", \"PyTorch\", \"NLP\"],\n      github: \"https://github.com/LazyCr0w/logbert\",\n      highlights: [\"BERT Architecture\", \"Anomaly Detection\", \"Cybersecurity\", \"Deep Learning\"],\n      category: \"Cybersecurity AI\"\n    },\n    {\n      title: \"Weather Application\",\n      description: \"Real-time weather application with clean UI and location-based forecasts. Features current conditions, hourly forecasts, and weather alerts.\",\n      icon: <Cloud className=\"w-8 h-8\" />,\n      technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"Weather API\"],\n      github: \"https://github.com/LazyCr0w/Weather-App\",\n      highlights: [\"Real-time Data\", \"Location-based\", \"Responsive Design\", \"Weather Alerts\"],\n      category: \"Web Development\"\n    },\n    {\n      title: \"QR Code Generator\",\n      description: \"Simple and efficient QR code generator with customizable options for size, color, and error correction levels. Clean interface for easy use.\",\n      icon: <QrCode className=\"w-8 h-8\" />,\n      technologies: [\"CSS\", \"JavaScript\", \"QR Library\"],\n      github: \"https://github.com/LazyCr0w/QR-Generator\",\n      highlights: [\"Customizable Options\", \"Multiple Formats\", \"Clean Interface\", \"Fast Generation\"],\n      category: \"Utility Tools\"\n    }\n  ];\n\n  return (\n    <section id=\"projects\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gradient-text\">\n            Featured Projects\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            A showcase of my work in Data Science, Machine Learning, and Software Development. \n            Each project demonstrates different aspects of my technical expertise and problem-solving approach.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-8\">\n          {projects.map((project, index) => (\n            <motion.div\n              key={project.title}\n              initial={{ opacity: 0, y: 50 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"glass rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group\"\n            >\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"text-blue-400 group-hover:text-purple-400 transition-colors\">\n                    {project.icon}\n                  </div>\n                  <div>\n                    <span className=\"text-xs text-gray-400 uppercase tracking-wide\">\n                      {project.category}\n                    </span>\n                    <h3 className=\"text-xl font-semibold text-white\">\n                      {project.title}\n                    </h3>\n                  </div>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <a\n                    href={project.github}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"p-2 glass rounded-lg hover:bg-white/10 transition-all duration-300\"\n                  >\n                    <Github className=\"w-5 h-5 text-gray-400 hover:text-white\" />\n                  </a>\n                </div>\n              </div>\n\n              <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                {project.description}\n              </p>\n\n              <div className=\"mb-4\">\n                <h4 className=\"text-sm font-semibold text-gray-200 mb-2\">Key Highlights:</h4>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {project.highlights.map((highlight) => (\n                    <div key={highlight} className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full\"></div>\n                      <span className=\"text-sm text-gray-300\">{highlight}</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-2\">\n                {project.technologies.map((tech) => (\n                  <span\n                    key={tech}\n                    className=\"px-3 py-1 bg-gray-800/50 rounded-full text-xs text-gray-300 border border-gray-700\"\n                  >\n                    {tech}\n                  </span>\n                ))}\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <a\n            href=\"https://github.com/LazyCr0w\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center space-x-2 px-8 py-4 glass rounded-full hover:bg-white/10 transition-all duration-300\"\n          >\n            <Github className=\"w-5 h-5\" />\n            <span>View All Projects on GitHub</span>\n            <ExternalLink className=\"w-4 h-4\" />\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,cAAc;gBAAC;gBAAU;gBAAgB;gBAAW;gBAAa;aAAQ;YACzE,QAAQ;YACR,YAAY;gBAAC;gBAAkB;gBAAwB;gBAAsB;aAAsB;YACnG,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,cAAc;gBAAC;gBAAU;gBAAU;gBAAY;gBAAQ;aAAe;YACtE,QAAQ;YACR,YAAY;gBAAC;gBAAyB;gBAA0B;gBAAkB;aAAqB;YACvG,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,cAAc;gBAAC;gBAAU;gBAAU;gBAAS;aAA0B;YACtE,QAAQ;YACR,YAAY;gBAAC;gBAA2B;gBAA2B;gBAAmB;aAA+B;YACrH,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,cAAc;gBAAC;gBAAU;gBAAQ;gBAAgB;gBAAW;aAAM;YAClE,QAAQ;YACR,YAAY;gBAAC;gBAAqB;gBAAqB;gBAAiB;aAAgB;YACxF,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,cAAc;gBAAC;gBAAQ;gBAAO;gBAAc;aAAc;YAC1D,QAAQ;YACR,YAAY;gBAAC;gBAAkB;gBAAkB;gBAAqB;aAAiB;YACvF,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,cAAc;gBAAC;gBAAO;gBAAc;aAAa;YACjD,QAAQ;YACR,YAAY;gBAAC;gBAAwB;gBAAoB;gBAAmB;aAAkB;YAC9F,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI;;;;;;8DAEf,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ;;;;;;sEAEnB,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAM,QAAQ,MAAM;gDACpB,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKxB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,0BACvB,8OAAC;oDAAoB,WAAU;;sEAC7B,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;mDAFjC;;;;;;;;;;;;;;;;8CAQhB,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;;2BApDN,QAAQ,KAAK;;;;;;;;;;8BA+DxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;0CACN,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Contact.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Mail, Github, Linkedin, MapPin, Phone, Send } from 'lucide-react';\nimport { useState } from 'react';\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log('Form submitted:', formData);\n    // You can integrate with a form service like Formspree, Netlify Forms, etc.\n  };\n\n  const contactInfo = [\n    {\n      icon: <Mail className=\"w-6 h-6\" />,\n      title: \"Email\",\n      value: \"<EMAIL>\",\n      link: \"mailto:<EMAIL>\"\n    },\n    {\n      icon: <Github className=\"w-6 h-6\" />,\n      title: \"GitHub\",\n      value: \"github.com/LazyCr0w\",\n      link: \"https://github.com/LazyCr0w\"\n    },\n    {\n      icon: <Linkedin className=\"w-6 h-6\" />,\n      title: \"LinkedIn\",\n      value: \"linkedin.com/in/sumon-deb-*********\",\n      link: \"https://www.linkedin.com/in/sumon-deb-*********\"\n    },\n    {\n      icon: <MapPin className=\"w-6 h-6\" />,\n      title: \"Location\",\n      value: \"Available for Remote Work\",\n      link: null\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gradient-text\">\n            Get In Touch\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            I'm always interested in discussing new opportunities, collaborations, or just chatting about \n            Data Science and Machine Learning. Feel free to reach out!\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold mb-6 gradient-text\">\n                Let's Connect\n              </h3>\n              <p className=\"text-gray-300 mb-8 leading-relaxed\">\n                Whether you're looking for a Data Scientist, have a project in mind, or want to discuss \n                the latest in ML research, I'd love to hear from you. I'm currently seeking opportunities \n                to apply my skills in a professional environment.\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={info.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center space-x-4 p-4 glass rounded-xl hover:bg-white/10 transition-all duration-300\"\n                >\n                  <div className=\"text-blue-400\">\n                    {info.icon}\n                  </div>\n                  <div>\n                    <h4 className=\"text-white font-semibold\">{info.title}</h4>\n                    {info.link ? (\n                      <a\n                        href={info.link}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n                      >\n                        {info.value}\n                      </a>\n                    ) : (\n                      <p className=\"text-gray-300\">{info.value}</p>\n                    )}\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"glass rounded-xl p-6\"\n            >\n              <h4 className=\"text-lg font-semibold mb-4 gradient-text\">\n                What I'm Looking For\n              </h4>\n              <ul className=\"space-y-2 text-gray-300\">\n                <li className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                  <span>Data Scientist positions</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                  <span>Machine Learning Engineer roles</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-cyan-400 rounded-full\"></div>\n                  <span>Research collaborations</span>\n                </li>\n                <li className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                  <span>Freelance ML projects</span>\n                </li>\n              </ul>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"glass rounded-2xl p-8\"\n          >\n            <h3 className=\"text-2xl font-bold mb-6 gradient-text\">\n              Send a Message\n            </h3>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n                    placeholder=\"Your Name\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n              <div>\n                <label htmlFor=\"subject\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Subject\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"subject\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n                  placeholder=\"What's this about?\"\n                />\n              </div>\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Message\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  required\n                  rows={6}\n                  className=\"w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400 resize-none\"\n                  placeholder=\"Tell me about your project or opportunity...\"\n                />\n              </div>\n              <motion.button\n                type=\"submit\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg text-white font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 flex items-center justify-center space-x-2 glow\"\n              >\n                <Send className=\"w-5 h-5\" />\n                <span>Send Message</span>\n              </motion.button>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,4EAA4E;IAC9E;IAEA,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAOpD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4B,KAAK,KAAK;;;;;;wDACnD,KAAK,IAAI,iBACR,8OAAC;4DACC,MAAM,KAAK,IAAI;4DACf,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAET,KAAK,KAAK;;;;;iFAGb,8OAAC;4DAAE,WAAU;sEAAiB,KAAK,KAAK;;;;;;;;;;;;;2CAtBvC,KAAK,KAAK;;;;;;;;;;8CA6BrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAIlB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,QAAQ;oDACR,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 2522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Port/portfolio/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Github, Linkedin, Mail, Heart } from 'lucide-react';\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const socialLinks = [\n    {\n      icon: <Github className=\"w-5 h-5\" />,\n      href: \"https://github.com/LazyCr0w\",\n      label: \"GitHub\"\n    },\n    {\n      icon: <Linkedin className=\"w-5 h-5\" />,\n      href: \"https://www.linkedin.com/in/sumon-deb-*********\",\n      label: \"LinkedIn\"\n    },\n    {\n      icon: <Mail className=\"w-5 h-5\" />,\n      href: \"mailto:<EMAIL>\",\n      label: \"Email\"\n    }\n  ];\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const handleNavClick = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <footer className=\"relative py-12 border-t border-gray-800\">\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent\"></div>\n      \n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h3 className=\"text-2xl font-bold gradient-text\">Sumon Deb</h3>\n            <p className=\"text-gray-300 leading-relaxed\">\n              Data Science & Machine Learning enthusiast passionate about creating \n              intelligent solutions that make a real-world impact.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((link, index) => (\n                <motion.a\n                  key={link.label}\n                  href={link.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"p-3 glass rounded-full hover:bg-white/10 transition-all duration-300 group\"\n                  aria-label={link.label}\n                >\n                  <div className=\"text-gray-400 group-hover:text-blue-400 transition-colors\">\n                    {link.icon}\n                  </div>\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-lg font-semibold text-white\">Quick Links</h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <button\n                    onClick={() => handleNavClick(link.href)}\n                    className=\"text-gray-300 hover:text-blue-400 transition-colors duration-300\"\n                  >\n                    {link.name}\n                  </button>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.4 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-lg font-semibold text-white\">Get In Touch</h4>\n            <div className=\"space-y-2 text-gray-300\">\n              <p>Available for remote opportunities</p>\n              <p>Open to collaborations</p>\n              <p>Always learning something new</p>\n            </div>\n            <div className=\"glass rounded-lg p-4\">\n              <p className=\"text-sm text-gray-300 mb-2\">Currently exploring:</p>\n              <div className=\"flex flex-wrap gap-2\">\n                <span className=\"px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-xs\">\n                  Deep Learning\n                </span>\n                <span className=\"px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-xs\">\n                  MLOps\n                </span>\n                <span className=\"px-2 py-1 bg-cyan-500/20 text-cyan-300 rounded text-xs\">\n                  Computer Vision\n                </span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-12 pt-8 border-t border-gray-800 text-center\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-sm\">\n              © {currentYear} Sumon Deb. All rights reserved.\n            </p>\n            <div className=\"flex items-center space-x-2 text-gray-400 text-sm\">\n              <span>Built with</span>\n              <Heart className=\"w-4 h-4 text-red-400\" />\n              <span>using Next.js & Tailwind CSS</span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,MAAM;YACN,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;YACN,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;gDACV,cAAY,KAAK,KAAK;0DAEtB,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;+CAZP,KAAK,KAAK;;;;;;;;;;;;;;;;0CAoBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC;oDACC,SAAS,IAAM,eAAe,KAAK,IAAI;oDACvC,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DACH,8OAAC;0DAAE;;;;;;0DACH,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyD;;;;;;kEAGzE,8OAAC;wDAAK,WAAU;kEAA6D;;;;;;kEAG7E,8OAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAwB;wCAChC;wCAAY;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}