# 📧 Contact Form Setup Guide

Your contact form is currently set up but needs a backend service to actually send emails. Here are the best options:

## 🚀 Option 1: Formspree (Recommended - FREE)

### Step 1: Create Formspree Account
1. Go to [formspree.io](https://formspree.io)
2. Sign up with your email (<EMAIL>)
3. Create a new form

### Step 2: Get Your Form ID
1. After creating the form, you'll get a Form ID (looks like: `xpzgkqyw`)
2. Copy this ID

### Step 3: Update Your Code
1. Open `src/components/Contact.tsx`
2. Find line with `'https://formspree.io/f/YOUR_FORM_ID'`
3. Replace `YOUR_FORM_ID` with your actual Form ID
4. Example: `'https://formspree.io/f/xpzgkqyw'`

### Step 4: Test
1. Save the file
2. Test your contact form
3. You should receive <NAME_EMAIL>

---

## 🚀 Option 2: Netlify Forms (If deploying on Netlify)

### Step 1: Add Netlify Form Attributes
Replace the form tag in `Contact.tsx` with:
```jsx
<form onSubmit={handleSubmit} className="space-y-6" netlify data-netlify="true" name="contact">
  <input type="hidden" name="form-name" value="contact" />
  {/* rest of your form fields */}
</form>
```

### Step 2: Update Submit Handler
```jsx
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  
  const formData = new FormData();
  formData.append('form-name', 'contact');
  formData.append('name', formData.name);
  formData.append('email', formData.email);
  formData.append('subject', formData.subject);
  formData.append('message', formData.message);

  try {
    await fetch('/', {
      method: 'POST',
      body: formData,
    });
    alert('Message sent successfully!');
  } catch (error) {
    alert('Failed to send message.');
  }
};
```

---

## 🚀 Option 3: EmailJS (Client-side only)

### Step 1: Install EmailJS
```bash
npm install @emailjs/browser
```

### Step 2: Setup EmailJS Account
1. Go to [emailjs.com](https://www.emailjs.com)
2. Create account and get your keys
3. Set up email service (Gmail, etc.)

### Step 3: Update Contact Component
```jsx
import emailjs from '@emailjs/browser';

const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  
  emailjs.send(
    'YOUR_SERVICE_ID',
    'YOUR_TEMPLATE_ID',
    {
      from_name: formData.name,
      from_email: formData.email,
      subject: formData.subject,
      message: formData.message,
      to_email: '<EMAIL>',
    },
    'YOUR_PUBLIC_KEY'
  ).then(() => {
    alert('Message sent successfully!');
    setFormData({ name: '', email: '', subject: '', message: '' });
  }).catch(() => {
    alert('Failed to send message.');
  });
};
```

---

## 🎯 Quick Start (Formspree - 5 minutes)

1. **Go to formspree.io** → Sign up
2. **Create new form** → Copy the Form ID
3. **Edit Contact.tsx** → Replace `YOUR_FORM_ID` with your actual ID
4. **Test the form** → You'll receive emails!

## 📝 Current Status

- ✅ Contact form UI is complete
- ✅ Form validation is working
- ⏳ Backend email service needs setup (choose option above)
- ✅ All your contact info is correctly displayed

## 🔧 Alternative: Direct Email Links

If you prefer a simpler approach, I can also update the contact form to just open the user's email client with a pre-filled <NAME_EMAIL>.

Choose your preferred option and I'll help you implement it!
